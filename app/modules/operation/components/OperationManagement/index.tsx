import { Plus } from "lucide-react";
import { useState } from "react";
import OperationTable from "../OperationTable";
import CreateOperationModal from "../CreateOperationModal";

export default function OperationManagement() {
	const [isCreateOpen, setIsCreateOpen] = useState(false);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Gestión de Operaciones</h1>
				<button
					type="button"
					className="btn btn-primary"
					onClick={() => setIsCreateOpen(true)}
				>
					<Plus size={16} />
					Nueva Operación
				</button>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<OperationTable />
				</div>
			</div>

			<CreateOperationModal isOpen={isCreateOpen} setIsOpen={setIsCreateOpen} />
		</div>
	);
}
